import { UploadModule } from '@/libs/request/upload'

/**
 * 文件过滤器类型定义
 */
export interface FileFilter {
  name: string
  extensions: string[]
}

/**
 * 文件夹选择配置
 */
export interface FolderSelectOptions {
  /**
   * 默认路径
   */
  defaultPath?: string
  /**
   * 标题
   */
  title?: string
  /**
   * 是否允许多选
   */
  multiple?: boolean
}

/**
 * 文件选择配置
 */
export interface FileSelectOptions {
  /**
   * 文件过滤器
   */
  filters?: FileFilter[]
  /**
   * 是否允许多选
   */
  multiple?: boolean
  /**
   * 默认路径
   */
  defaultPath?: string
  /**
   * 标题
   */
  title?: string
}

/**
 * 文件夹选择配置
 */
export interface FolderSelectOptions {
  /**
   * 默认路径
   */
  defaultPath?: string
  /**
   * 标题
   */
  title?: string
  /**
   * 是否允许多选
   */
  multiple?: boolean
}

/**
 * 上传结果
 */
export interface UploadResult {
  /**
   * 是否成功
   */
  success: boolean
  /**
   * 文件URL
   */
  url?: string
  /**
   * 错误信息
   */
  error?: string
  /**
   * 文件名
   */
  fileName?: string
  /**
   * 上传进度信息（仅在成功时返回）
   */
  progress?: number
  /**
   * 对象ID（从STS签名中获取）
   */
  objectId?: string
}

/**
 * 上传配置
 */
export interface UploadOptions {
  /**
   * 进度回调
   */
  onProgress?: (progress: number) => void
  /**
   * 自定义文件名
   */
  fileName?: string
  /**
   * 文件目录前缀
   */
  dirPrefix?: string
}

/**
 * IPC传输用的上传配置（不包含回调函数）
 */
export interface UploadOptionsForIPC {
  /**
   * 自定义文件名
   */
  fileName?: string
  /**
   * 文件目录前缀
   */
  dirPrefix?: string
}

/**
 * 上传文件载荷
 */
export interface UploadFilePayload {
  filePath: string
  options?: UploadOptionsForIPC
}

/**
 * 上传Buffer载荷
 */
export interface UploadBufferPayload {
  buffer: number[] | Uint8Array | Buffer
  fileName: string
  options?: UploadOptionsForIPC
  folderUuid?: string
  fileMd5?: string

  /**
   * 上传任务ID，用于标识进度回调
   */
  uploadId?: string
  module?: UploadModule
}

export interface FileUploaderIPCClient {

  /**
   * 上传Buffer到OSS
   * @param buffer 文件Buffer
   * @param fileName 文件名
   * @param options 上传配置
   * @returns 上传结果
   */
  uploadBufferToOSS: (data: UploadBufferPayload) => Promise<UploadResult>
}
