import React, { useMemo, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { Search, RefreshCw, ArrowDownAz, Link, ClipboardCheck } from 'lucide-react'
import { DateRangePicker } from '@/components/date-range-picker'
import { DateRange } from 'react-day-picker'
import { useParams, useSearchParams } from 'react-router'
import { useInfiniteQueryScriptList } from '@/hooks/queries/useQueryScript'
import { cn } from '@/components/lib/utils'
import { Work } from '@/types/project'
import { useInfiniteQueryWorkList } from '@/hooks/queries/useQueryWork'
import { Selects } from './selects'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'

function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

function WorkCard({
  work,
  isSelected,
  onSelect,
}: {
  work: Work
  isSelected: boolean
  onSelect: (id: number, selected: boolean) => void
}) {
  return (
    <div className="group aspect-[3/4] flex flex-col rounded-lg shadow-sm border hover:shadow-md transition-all">
      <div className="relative flex-1 w-full bg-muted rounded-t-lg overflow-hidden group aspect-auto">
        <img src={work.cover} alt={work.name} className="h-full w-full object-cover" draggable="false" />

        <div
          className="absolute inset-0 z-10 flex flex-col gap-1 p-2 text-xs text-white leading-none
          [&_.tag]:bg-black/40 [&_.tag]:rounded [&_.tag]:py-0.5 [&_.tag]:px-1"
        >
          <div className="flex gap-1 items-center">
            <span className="tag">重复率 {work.repetitionRate / 100}%</span>
            <span className="tag">已合成</span>
            <Checkbox
              className="border-primary ml-auto"
              checked={isSelected}
              onCheckedChange={checked => onSelect(work.id, !!checked)}
            />
          </div>
          <div className="flex gap-1 items-center">
            <span className="tag flex gap-0.5 items-center">
              <Link className="size-3" />
              <span>关联任务</span>
              <span>1</span>
            </span>
            <span className="tag">ID: {work.id}</span>
          </div>
          <div className="mt-auto flex items-center">
            <span className="tag">{formatDuration(work.duration)}</span>
            <span className="tag ml-auto flex gap-0.5 items-center">
              <ClipboardCheck className="size-3" />
              <span>审片批注</span>
              <span>1</span>
            </span>
          </div>
        </div>
      </div>

      {/* 作品信息 */}
      <div className="relative p-2 w-full min-w-0 flex flex-col items-start justify-between gap-1">
        <span className="block font-medium text-sm truncate w-full" title={work.name}>
          {work.name}
        </span>
        <span className="block border border-primary bg-primary/10 text-primary text-xs leading-none p-0.5 rounded">
          未审核
        </span>
        <Button
          variant="default"
          size="sm"
          className="absolute h-6 right-2 bottom-2 opacity-0 group-hover:opacity-100 transition-all"
        >
          下载视频
        </Button>
      </div>
    </div>
  )
}

function Sidebar() {
  const params = useParams()
  const { data } = useInfiniteQueryScriptList({ projectId: params.projectId })
  const scripts = useMemo(() => data?.pages.flatMap(v => v.list) || [], [data])
  const [searchParams, setSearchParams] = useSearchParams()
  const selectedScriptId = searchParams.get('scriptId') ?? undefined

  function Item({ title, id }: { title: string; id?: string }) {
    return (
      <Button
        variant="ghost"
        size="lg"
        className={cn(
          'flex justify-start px-4',
          selectedScriptId === id && 'bg-primary/20 hover:bg-primary/17 text-primary',
        )}
        onClick={() => setSearchParams(id ? `?scriptId=${id}` : '')}
      >
        {title}
      </Button>
    )
  }

  return (
    <div className="w-60 border-r flex flex-col p-4 gap-1">
      <Item title="全部脚本" />
      {scripts.map(script => (
        <Item key={script.id} title={script.title} id={script.id.toString()} />
      ))}
    </div>
  )
}

export default function Works() {
  const params = useParams()
  const queryClient = useQueryClient()
  const [searchParams] = useSearchParams()
  const [keyword, setKeyword] = useState('')
  const [selectedWorks, setSelectedWorks] = useState<Set<number>>(new Set())
  const [selectAll, setSelectAll] = useState(false)
  const [range, setRange] = useState<DateRange | undefined>(undefined)

  const createAt = useMemo((): string | undefined => {
    if (!range?.from || !range?.to) return undefined
    return `${range.from.getTime()},${range.to.getTime() + 86400000}`
  }, [range])

  const { data } = useInfiniteQueryWorkList({
    projectId: params.projectId,
    scriptId: searchParams.get('scriptId') ?? undefined,
    keyword,
    createAt,
  })
  const works = useMemo(() => data?.pages.flatMap(v => v.list) || [], [data])

  // 处理单个作品选择
  const handleWorkSelect = (id: number, selected: boolean) => {
    const newSelected = new Set(selectedWorks)
    if (selected) {
      newSelected.add(id)
    } else {
      newSelected.delete(id)
    }
    setSelectedWorks(newSelected)
    setSelectAll(newSelected.size === works.length)
  }

  // 处理全选
  const handleSelectAll = (selected: boolean) => {
    if (works.length === 0) return
    if (selected) {
      setSelectedWorks(new Set(works.map(work => work.id)))
    } else {
      setSelectedWorks(new Set())
    }
    setSelectAll(selected)
  }

  return (
    <div className="flex flex-col flex-1 min-h-0">
      <div className="flex flex-wrap items-center gap-4 p-4 border-b">
        <div className="absolute right-4 top-4">
          <Input
            placeholder="请输入关键词搜索"
            value={keyword}
            onChange={e => setKeyword(e.target.value)}
            className="pr-10"
          />
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground" />
        </div>

        <div className="flex w-full items-center gap-2 p-px">
          <DateRangePicker value={range} onChange={setRange} />
          <Button variant="default" className="ml-auto" disabled={selectedWorks.size === 0}>
            分享视频
          </Button>
          <Button variant="default" disabled={selectedWorks.size === 0}>
            合成视频
          </Button>
          <Button variant="default" disabled={selectedWorks.size === 0}>
            下载视频
          </Button>
        </div>

        <Selects />
      </div>

      <div className="flex flex-1 overflow-auto">
        <Sidebar />

        <div className="flex-1 flex flex-col overflow-auto">
          <div className="flex gap-4 items-center justify-between sticky top-0 z-20 bg-main p-4">
            <span className="text-sm text-muted-foreground">作品总数: {works.length}</span>
            <Button size="sm" variant="outline" className="ml-auto">
              <ArrowDownAz className="size-3.5 mr-1" />
              排序
            </Button>
            <div className="flex items-center gap-2">
              <Checkbox checked={selectAll} onCheckedChange={handleSelectAll} />
              <span className="text-sm">已选择: {selectedWorks.size}个</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="-ml-2"
              onClick={() => queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.WORK_LIST] })}
            >
              <RefreshCw className="size-3.5 mr-1 rotate-x-180 text-primary" />
              <span className="text-muted-foreground">刷新</span>
            </Button>
          </div>

          {works.length > 0 ? (
            <div className="px-4 pb-4 grid gap-4 grid-cols-[repeat(auto-fill,minmax(12rem,1fr))]">
              {works.map(work => (
                <WorkCard
                  key={work.id}
                  work={work}
                  isSelected={selectedWorks.has(work.id)}
                  onSelect={handleWorkSelect}
                />
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
              <div className="text-lg mb-2">暂无作品</div>
              <div className="text-sm">请调整筛选条件或创建新的作品</div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
