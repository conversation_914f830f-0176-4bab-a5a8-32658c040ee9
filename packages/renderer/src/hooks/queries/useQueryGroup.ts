import { QUERY_KEYS } from '@/constants/queryKeys'
import { GroupAPI } from '@/libs/request/api/group'
import useInfiniteQuery, { PaginationQueryParams } from '../useInfiniteQuery'
import { useQuery } from '@tanstack/react-query'

export const useCurrentGroup = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.GROUP_CURRENT],
    queryFn: () => GroupAPI.current({}),
  })
}

export const useInfiniteQueryGroupList = (params: PaginationQueryParams) => {
  return useInfiniteQuery([QUERY_KEYS.GROUP_LIST], GroupAPI.list, params, {
    pageSize: params.pageSize || 12,
  })
}
