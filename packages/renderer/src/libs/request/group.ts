import { GroupManager } from '../storage'
import { GroupAPI } from './api/group'

async function check(id: number | null) {
  if (!id) return false
  try {
    return await GroupAPI.check({ teamId: id })
  } catch {
    return false
  }
}

export async function refreshGroup() {
  let valid = await check(GroupManager.current())
  if (valid) return valid
  GroupManager.clear()
  const list = await GroupAPI.list({})
  if (!list.length) return false
  valid = await check(list[0].id)
  if (valid) GroupManager.switch(list[0].id)
  return valid
}
