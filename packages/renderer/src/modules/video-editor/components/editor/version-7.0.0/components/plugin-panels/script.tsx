import React, { useMemo } from 'react'
import { ScriptSceneData, useQueryScript } from '@/hooks/queries/useQueryScript.ts'
import { useEditorContext } from '@rve/editor/contexts'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Edit, Volume2 } from 'lucide-react'
import useVirtualTabsStore from '@/stores/useVirtualTabsStore.ts'

// 脚本段落组件
export const ScriptSection: React.FC<{
  scene: ScriptSceneData
  index: number
}> = ({ scene, index }) => {
  // 将脚本内容按行分割成句子
  const sentences = useMemo(() => {
    if (!scene.script) return []
    return scene.script
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
  }, [scene.script])

  if (sentences.length === 0) return null

  return (
    <div className="mb-6">
      {/* 段落标题 */}
      <div className="flex items-center gap-2 mb-3">
        <h3 className="text-md font-medium">
          #{index + 1} {scene.title}
        </h3>
        <div className="text-xs text-gray-500">
          台词: 预计时长 00:0{Math.min(sentences.length + 1, 9)}
        </div>
      </div>

      {/* 句子列表 */}
      <SentenceList sentences={sentences} />

      {/* 画面说明 */}
      {scene.notes && (
        <div className="mt-3">
          <div className="text-xs font-medium text-gray-700 mb-1">画面说明</div>
          <div className="text-xs text-gray-600  p-2 rounded">
            {scene.notes}
          </div>
        </div>
      )}

      {/* 拍摄示例 */}
      {/*<div className="mt-3">*/}
      {/*  <div className="text-xs font-medium text-gray-700 mb-1">拍摄示例  </div>*/}
      {/*  <div className="text-xs text-gray-600" />*/}
      {/*</div>*/}
    </div>
  )
}

// 句子列表组件
export const SentenceList: React.FC<{
  sentences: string[]
}> = ({ sentences }) => {
  return (
    <div className="space-y-1">

      {sentences.map((sentence, index) => (
        <div key={index} className="flex items-start gap-2 text-xs">
          <span className="text-gray-400 text-xs mt-0.5 min-w-[20px]">
            {index + 1}.
          </span>
          <span className="text-gray-400 leading-relaxed">
            {sentence}
          </span>
        </div>
      ))}
    </div>
  )
}

const ScriptPanel = () => {
  const { scriptId } = useEditorContext()
  const { data: script } = useQueryScript(scriptId)
  const { pushTab } = useVirtualTabsStore()
  const scenes = script?.content || []

  // 过滤出有内容的场景
  const validScenes = useMemo(() => {
    return scenes.filter(scene => scene.script && scene.script.trim().length > 0)
  }, [scenes])

  return (
    <div className="h-full flex flex-col w-full">
      {/* 顶部工具栏 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="text-xs h-7 px-3 "
          >
            <Volume2 className="w-3 h-3 mr-1" />
            朗读全部台词
          </Button>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="text-xs h-7 px-3 text-gray-600 hover:bg-gray-100"
          onClick={() => pushTab({
            componentKey: 'Script',
            params: { scriptId: script?.id }
          })}
        >
          <Edit className="w-3 h-3 mr-1" />
          编辑脚本
        </Button>
      </div>

      {/* 脚本内容区域 */}
      <ScrollArea className="flex-1">
        <div className="p-4">
          {validScenes.length > 0 ? (
            validScenes.map((scene, index) => (
              <ScriptSection
                key={scene.id}
                scene={scene}
                index={index}
              />
            ))
          ) : (
            <div className="flex flex-col items-center justify-center h-40 text-gray-500">
              <div className="text-sm">暂无脚本内容</div>
              <div className="text-xs mt-1">请先添加脚本内容</div>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}

export default React.memo(ScriptPanel)
