import { lazy } from 'react'
import { create } from 'zustand'
import { nanoid } from 'nanoid'
import { persist } from 'zustand/middleware'
import { pick } from 'lodash'
import { z } from 'zod'

export const VirtualizableComponents = {
  Mixcut: lazy(() => import('@/pages/Mixcut/Mixcut.page.tsx')),
  Editor: lazy(() => import('@/pages/VideoEditor')),
  Script: lazy(() => import('@/pages/Projects/creative/script')),

  /**
   * @deprecated
   */
  Setting: lazy(() => import('@/pages/SettingsPage')),
} as const

type VirtualizableComponentKeys = keyof typeof VirtualizableComponents

// 使用 Zod 定义每种组件类型的参数验证模式
const EditorParamsSchema = z.object({
  scriptId: z.string().min(1, '脚本ID不能为空'),
  projectId: z.string().optional(),
  name: z.string().optional(), // 用于显示在标题中
})

const MixcutParamsSchema = z.object({
  scriptId: z.string().min(1, '脚本ID不能为空'),
  name: z.string().optional(), // 用于显示在标题中
})

const ScriptParamsSchema = z.object({
  scriptId: z.string().min(1, '脚本ID不能为空'),
})

const SettingParamsSchema = z.object({})

// 参数验证模式映射
const ComponentParamsSchemas = {
  Editor: EditorParamsSchema,
  Mixcut: MixcutParamsSchema,
  Script: ScriptParamsSchema,
  Setting: SettingParamsSchema,
} as const

// 类型安全的参数映射
export type ParamsForComponent<T extends VirtualizableComponentKeys> = z.infer<typeof ComponentParamsSchemas[T]>

export interface VirtualTab {
  id: string
  componentKey: VirtualizableComponentKeys
  title: string
  keepAlive?: boolean
  closable?: boolean
  params: Record<string, any>

  // icon?: React.ReactNode

  /**
   * @deprecated
   */
  component?: never
}

interface VirtualTabsStore {
  tabs: VirtualTab[]
  activeTabId: string | null

  // 操作方法
  goToHomePage(): void
  pushTab(tab: Omit<VirtualTab, 'id' | 'title'> & { id?: string; title?: string }): void
  pushNamedTab<T extends VirtualizableComponentKeys>(name: T, params: ParamsForComponent<T>): string | undefined
  closeTab(id: string): void
  setActiveTab(id: string): void
  updateTab(id: string, updates: Partial<Omit<VirtualTab, 'id'>>): void

  // 批量操作
  closeOtherTabs(id: string): void
  closeAllTabs(): void
  closeTabsToRight(id: string): void
}

function generateTabMeta(tab: Pick<VirtualTab, 'componentKey' | 'params'> & { id?: string }) {
  const { componentKey, params = {} } = tab

  if (componentKey === 'Editor' || componentKey === 'Mixcut') {
    if (!('scriptId' in params)) {
      return null
    }
    const { scriptId } = params

    return {
      id: `${componentKey}-${scriptId}`,
      title: (componentKey === 'Editor' ? '视频编辑' : '视频混剪') + (params.name ? ` - ${params.name}` : '')
    }
  }

  if (componentKey === 'Script') {
    if (!('scriptId' in params)) {
      return null
    }

    const { scriptId } = params

    return {
      id: `${componentKey}-${scriptId}`,
      title: '脚本编辑'
    }
  }

  return {
    id: tab.id?.toString() || nanoid(),
    title: null
  }
}

/**
 * 验证组件参数
 * @param componentKey 组件键名
 * @param params 参数对象
 * @returns 验证结果
 */
function validateComponentParams<T extends VirtualizableComponentKeys>(
  componentKey: T,
  params: unknown
): { success: true; data: ParamsForComponent<T> } | { success: false; error: string } {
  try {
    const schema = ComponentParamsSchemas[componentKey]
    const validatedParams = schema.parse(params)
    return { success: true, data: validatedParams as ParamsForComponent<T> }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
      return { success: false, error: errorMessage }
    }
    return { success: false, error: '参数验证失败' }
  }
}

const useVirtualTabsStore = create<VirtualTabsStore>()(
  persist(
    (set, get) => ({
      tabs: [],
      activeTabId: null,

      goToHomePage: () => set({ activeTabId: null }),

      pushTab: tab => {
        const meta = generateTabMeta(tab)
        if (!meta) return

        const { id, title = '' } = meta

        const { closable = true } = tab
        const newTab = { ...tab, id, title, closable } as VirtualTab

        // 检查是否已存在相同 ID 的标签
        const { tabs } = get()
        const existingTabIndex = tabs.findIndex(t => t.id === id)

        set(state => {
          // 如果标签已存在，则激活它
          if (existingTabIndex !== -1) {
            return { ...state, activeTabId: id }
          }

          // 否则添加新标签并激活
          return {
            tabs: [...state.tabs, newTab],
            activeTabId: id,
          }
        })

        return id
      },

      pushNamedTab: <T extends VirtualizableComponentKeys>(name: T, params: ParamsForComponent<T>) => {
        // 验证参数
        const validation = validateComponentParams(name, params)
        if (!validation.success) {
          console.error(`参数验证失败: ${validation.error}`)
          return undefined
        }

        // 使用验证后的参数创建标签
        const tab = {
          componentKey: name,
          params: validation.data,
          closable: true,
        }

        const meta = generateTabMeta(tab)
        if (!meta) {
          console.error(`无法为组件 ${name} 生成标签元数据`)
          return undefined
        }

        const { id, title = '' } = meta
        const newTab = { ...tab, id, title } as VirtualTab

        // 检查是否已存在相同 ID 的标签
        const { tabs } = get()
        const existingTabIndex = tabs.findIndex(t => t.id === id)

        set(state => {
          // 如果标签已存在，则激活它
          if (existingTabIndex !== -1) {
            return { ...state, activeTabId: id }
          }

          // 否则添加新标签并激活
          return {
            tabs: [...state.tabs, newTab],
            activeTabId: id,
          }
        })

        return id
      },

      closeTab: id => {
        const { tabs, activeTabId } = get()
        const tabIndex = tabs.findIndex(tab => tab.id === id)

        // 如果标签不存在，直接返回
        if (tabIndex === -1) return

        // 如果关闭的是当前激活的标签，则需要激活其他标签
        let newActiveTabId = activeTabId
        if (activeTabId === id) {
          // 优先激活右侧标签，如果没有则激活左侧标签
          const newActiveIndex = tabIndex === tabs.length - 1
            ? Math.max(0, tabIndex - 1)
            : tabIndex + 1

          newActiveTabId = tabs.length > 1 ? tabs[newActiveIndex].id : null
        }

        set({
          tabs: tabs.filter(tab => tab.id !== id),
          activeTabId: newActiveTabId,
        })
      },

      setActiveTab: id => {
        set({ activeTabId: id })
      },

      updateTab: (id, updates) => {
        set(state => ({
          tabs: state.tabs.map(tab =>
            tab.id === id ? { ...tab, ...updates } : tab,
          ),
        }))
      },

      closeOtherTabs: id => {
        const { tabs } = get()
        const targetTab = tabs.find(tab => tab.id === id)

        if (targetTab) {
          set({
            tabs: [targetTab],
            activeTabId: id,
          })
        }
      },

      closeAllTabs: () => {
        set({
          tabs: [],
          activeTabId: null,
        })
      },

      closeTabsToRight: id => {
        const { tabs } = get()
        const tabIndex = tabs.findIndex(tab => tab.id === id)

        if (tabIndex !== -1) {
          const newTabs = tabs.slice(0, tabIndex + 1)

          set(state => ({
            tabs: newTabs,
            activeTabId: state.activeTabId && newTabs.some(tab => tab.id === state.activeTabId)
              ? state.activeTabId
              : id,
          }))
        }
      },
    }),
    {
      name: 'VirtualTabsStore',
      partialize: state => import.meta.env.DEV ? pick(state, 'tabs', 'activeTabId') : {},
    }
  )
)

export default useVirtualTabsStore
