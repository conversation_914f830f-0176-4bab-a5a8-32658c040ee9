import React, { <PERSON>actN<PERSON>, useEffect, useMemo, useState } from 'react'
import { UploadIcon, PlusIcon, FolderUpIcon, Edit, Trash, FolderInput } from 'lucide-react'
import { Button } from './ui/button'
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover'
import { FileUploader, FileUploaderRenderProps, UploadedFile } from '@/components/ui/file-uploader'
import { FolderUploader } from '@/components/ui/folder-uploader'
import { getPathChain, TreeNode } from '@/components/TreeList'
import { cn } from '@/components/lib/utils'
import MoveDialog from '@/pages/Projects/material/components/MoveDialog'
import { FolderAction } from '@/pages/Projects/material/components/MediaItem'
import { useItemActions } from '@/hooks/useItemActions'
import { LocalDirItem } from '@/modules/video-editor/components/editor/version-7.0.0/components/overlays/stickers/local-dir'
import { ResourceSource } from '@/types/resources'
import { ResourceModule } from '@/libs/request/api/resource'

export type Folder = {
  id: string
  name: string
}

export type LocalResourceItem = {
  id: string
  type: string
  thumbnail?: string
  name: string
  path?: string
}

export interface LocalResourcePanelProps {
  /**
   * 目录数据
   */
  dirList: TreeNode[]
  /**
   * 当前选中的目录
   */
  currentFolderId: string
  /**
   * 切换目录回调
   */
  onFolderChange: (folderId: string) => void // 新增: 切换目录回调
  /**
   * 本地资源类型
   */
  resourceType: ResourceSource
  /**
   * 本地资源文件夹类型
   */
  resourceFolderType: ResourceSource
  /**
   * 文件上传类型
   */
  fileUploadTypes: string[]

  // /**
  //  * 重命名文件夹
  //  */
  // renameDirectory: (
  //   id: string,
  //   title: string,
  //   options?: { label?: string; headerTitle?: string },
  // ) => void
  /**
   * 关键词
   */
  searchKey?: string
  /**
   * 上传完成回调
   */
  onUploadComplete?: (uploaded: UploadedFile[], folderId: string) => void
  /**
   * 资源列表
   */
  resources?: any
  /**
   * 是否显示上传按钮
   */
  showUpload?: boolean
  /**
   * 是否显示新建文件夹按钮
   */
  showCreateFolder?: boolean
  /**
   * 列表为空时的提示文本
   */
  emptyText?: string
  /**
   * 列表容器的类名
   */
  containerClassName?: string
  /**
   * 资源网格的列数
   */
  gridCols?: number
  /**
   * 是否加载中
   */
  isLoading?: boolean
  /**
   * 资源项渲染函数
   */
  renderResourceItem?: (resource: LocalResourceItem, index: number) => ReactNode
}
const CustomUploader: React.FC<FileUploaderRenderProps> = ({ getRootProps, getInputProps, isLoading }) => {
  return (
    <div className="flex items-center justify-center gap-2">
      <UploadIcon className="w-3.5 h-3.5" />
      <div {...getRootProps()}>
        <input {...getInputProps()} />
        {isLoading ? '上传中...' : '上传文件'}
      </div>
    </div>
  )
}
/**
 * 通用本地资源面板组件
 * 提供文件夹选择、上传文件、新建文件夹功能，以及资源列表展示
 */
export function LocalResourcePanel({
  dirList,
  currentFolderId,
  onFolderChange,
  resourceType,
  resourceFolderType,
  fileUploadTypes,
  searchKey = '',
  onUploadComplete,
  resources = [],
  showUpload = true,
  showCreateFolder = true,
  renderResourceItem,
  emptyText = '暂无资源',
  containerClassName = '',
  gridCols = 4,
  isLoading = false,
}: LocalResourcePanelProps) {
  const [moveDialogOpen, setMoveDialogOpen] = useState(false)
  const [moveId, setMoveId] = useState('') //被移动的文件夹id
  const [moveType, setMoveType] = useState(ResourceSource.FOLDER)
  const { createItem, renameItem, deleteLocalItem } = useItemActions()
  const flatResourcesLength = Array.isArray(resources?.pages)
    ? resources.pages.reduce((count, page) => count + (page?.list?.length || 0), 0)
    : 0
  const [processedResources, setProcessedResources] = useState<LocalResourceItem[]>([])

  //本地文件夹操作
  const LocalFolderActions: FolderAction[] = [
    {
      icon: <FolderInput className="w-4 h-4" />,
      label: '移动',
      onClick: (nodeId: string, _parentId: string, _label: string) => {
        setMoveType(resourceFolderType)
        setMoveId(nodeId)
        setMoveDialogOpen(true)
      },
    },
    {
      icon: <Edit className="w-4 h-4" />,
      label: '重命名',
      onClick: (nodeId: string, _parentId: string, label: string) => {
        renameItem(resourceFolderType, nodeId, label, {
          label: '文件夹名称',
          headerTitle: '文件夹',
        })
      },
    },
    {
      icon: <Trash className="w-4 h-4" />,
      label: '删除',
      onClick: (nodeId: string, _parentId: string, label: string) => {
        deleteLocalItem(resourceFolderType, nodeId, label)
      },
    },
  ]
  const LocalMediaActions: FolderAction[] = [
    {
      icon: <FolderInput className="w-4 h-4" />,
      label: '移动',
      onClick: (nodeId: string, _parentId: string, _label: string) => {
        setMoveType(ResourceSource.LOCAL_STICK)
        setMoveId(nodeId)
        setMoveDialogOpen(true)
      },
    },
    {
      icon: <Edit className="w-4 h-4" />,
      label: '重命名',
      onClick: (nodeId: string, _parentId: string, label: string) => {
        renameItem(resourceType, nodeId, label, {
          label: '素材名称',
          headerTitle: '素材',
        })
      },
    },
    {
      icon: <Trash className="w-4 h-4" />,
      label: '删除',
      onClick: (nodeId: string, _parentId: string, label: string) => {
        deleteLocalItem(resourceType, nodeId, label)
      },
    },
  ]

  const handleMoveConfirm = async (selectedNode: TreeNode) => {
    console.log('移动后回调', selectedNode)
    // 移动媒体文件
    // if (selectedMediaItems.size > 0) {
    //   await ResourceModule.media.move({
    //     fileIds: Array.from(selectedMediaItems),
    //     folderUuid: targetFolderId,
    //   })
    //   await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_MEDIA_LIST] })
    // }
  }

  // 获取目录链
  const folderPath = useMemo(() => {
    if (!currentFolderId || !dirList) return []
    return getPathChain(dirList, currentFolderId) ?? []
  }, [currentFolderId, dirList])

  //获得当前目录下的子目录
  const childFolders = useMemo(() => {
    if (!dirList) return []

    const findChildren = (nodes: any[]): any[] => {
      for (const node of nodes) {
        if (String(node.id) === String(currentFolderId)) {
          return node.children || []
        }

        if (node.children && node.children.length > 0) {
          const result = findChildren(node.children)
          if (result.length > 0) {
            return result
          }
        }
      }
      return []
    }
    let children = findChildren(dirList)

    if (searchKey && searchKey !== '') {
      const keyword = searchKey.toLowerCase()
      children = children.filter(child => child.label?.toLowerCase().includes(keyword))
    }

    return children
  }, [dirList, currentFolderId, searchKey])
  const extractFileId = (url: string) => {
    try {
      const parsedUrl = new URL(url)
      const pathSegments = parsedUrl.pathname.split('/')
      return pathSegments[pathSegments.length - 1] // 获取 URL 路径中的最后一部分作为 ID
    } catch (error) {
      console.error('URL 解析失败:', error)
      return ''
    }
  }

  //获取fileUrl 和 thumbUrl
  useEffect(() => {
    const fetchCovers = async () => {
      if (!resources?.pages) return

      const allResources = resources.pages.flatMap(page => page.list)

      const updatedResources = await Promise.all(
        allResources.map(async resource => {
          try {
            const replaceUrl = async (url: string) => {
              if (!url) return ''
              const objectId = extractFileId(url)
              const res = await ResourceModule.cover(objectId)
              const parser = new DOMParser()
              const doc = parser.parseFromString(res, 'text/html')
              const anchor = doc.querySelector('a')
              return anchor?.href || url // 如果解析失败，保持原 URL
            }

            const fileUrl = await replaceUrl(resource.content?.fileUrl)
            const thumbUrl = await replaceUrl(resource.content?.thumbUrl)

            return {
              ...resource,
              content: {
                ...resource.content,
                fileUrl,
                thumbUrl,
              },
            }
          } catch (error) {
            console.error('获取地址失败:', error)
            return resource
          }
        }),
      )

      setProcessedResources(updatedResources)
    }

    fetchCovers()
  }, [resources])

  //上传完成之后创建本地资源
  const handleUploadComplete = async (files: UploadedFile[]) => {
    const uploaded = files.filter(f => f.status === 'success')
    console.log('上传完成信息:', uploaded)
    if (onUploadComplete) {
      onUploadComplete(uploaded, currentFolderId)
    }
  }

  // 处理资源点击
  const onResourceClick = (resource: LocalResourceItem) => {
    console.log('点击资源', resource)
  }

  // 处理资源添加到时间轴
  const onResourceAdd = (resource: LocalResourceItem) => {
    console.log('添加资源到时间轴', resource)
  }

  // 默认的资源项渲染函数
  const defaultRenderResourceItem = (resource: LocalResourceItem, index: number) => {
    return (
      <div
        key={resource.id || index}
        className="aspect-square bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
        onClick={() => onResourceClick?.(resource)}
        onDoubleClick={() => onResourceAdd?.(resource)}
      >
        {resource.thumbnail ? (
          <img src={resource.thumbnail} alt={resource.name} className="w-full h-full object-cover rounded-lg" />
        ) : (
          <div className="text-gray-400 flex flex-col items-center justify-center p-2">
            <span className="text-xs text-center mt-1 truncate w-full">1{resource.name}11</span>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={`flex flex-col gap-4 mt-2 ${containerClassName}`}>
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2 w-full justify-between">
          {showUpload && (
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-1">
                  <UploadIcon className="w-3.5 h-3.5" />
                  <span>上传</span>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-36 p-0">
                <div className="py-1">
                  <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center gap-2">
                    <FileUploader
                      fileTypes={fileUploadTypes}
                      folderUuid={currentFolderId}
                      onUpload={handleUploadComplete}
                      renderCustomComponent={props => <CustomUploader {...props} />}
                    />
                  </button>
                  <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center gap-2">
                    <FolderUploader
                      folderUuid={currentFolderId}
                      children={
                        <div className="flex items-center justify-center">
                          <FolderUpIcon className="w-3.5 h-3.5 mr-2" />
                          上传文件夹
                        </div>
                      }
                      isShowUploadedFiles={false}
                      showFileList={false}
                      onProgress={(current, total) => {
                        console.log({ current, total })
                      }}
                      onUpload={handleUploadComplete}
                    />
                  </button>
                </div>
              </PopoverContent>
            </Popover>
          )}
          {showCreateFolder && (
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
              onClick={() =>
                createItem(resourceFolderType, currentFolderId, {
                  label: '文件夹名称',
                  headerTitle: '文件夹',
                })
              }
            >
              <PlusIcon className="w-3.5 h-3.5" />
              <span>新建文件夹</span>
            </Button>
          )}
        </div>
      </div>
      <div>
        <div className="flex items-center space-x-1 text-white">
          {folderPath.map((folder, index) => (
            <React.Fragment key={folder.id}>
              <button
                className={cn('hover:underline', {
                  'text-primary-highlight1': folder.id === currentFolderId,
                })}
                onClick={() => onFolderChange(folder.id)}
              >
                {folder.label}
              </button>
              {index < folderPath.length - 1 && <span>{'>'}</span>}
            </React.Fragment>
          ))}
        </div>
      </div>
      {/* 资源列表区域 */}
      {isLoading ? (
        <div className="flex justify-center items-center h-40">
          <div className="text-gray-400">加载中...</div>
        </div>
      ) : (
        <>
          {childFolders.length > 0 || flatResourcesLength > 0 ? (
            <div className={`grid grid-cols-${gridCols} gap-3 mb-4`}>
              {/* 渲染子文件夹 */}
              {childFolders.map(folder => (
                <LocalDirItem
                  key={folder.id}
                  folder={folder}
                  currentFolderId={currentFolderId}
                  onFolderChange={onFolderChange}
                  LocalActions={LocalFolderActions}
                />
              ))}
              {/* 渲染本地资源 */}
              {/* {processedResources.map((resource, index) =>
                renderResourceItem ? renderResourceItem(resource, index) : defaultRenderResourceItem(resource, index),
              )} */}
              {processedResources.map((resource, index) => (
                <LocalDirItem
                  key={resource.id || index}
                  resource={resource}
                  isResource={true}
                  index={index}
                  currentFolderId={currentFolderId}
                  onFolderChange={onFolderChange}
                  LocalActions={LocalMediaActions}
                  renderResourceItem={renderResourceItem}
                  defaultRenderResourceItem={defaultRenderResourceItem}
                />
              ))}
            </div>
          ) : (
            <div className="flex justify-center items-center h-40">
              <div className="text-gray-400">{emptyText}</div>
            </div>
          )}
        </>
      )}
      <MoveDialog
        open={moveDialogOpen}
        moveId={moveId}
        dirList={dirList}
        moveType={moveType}
        onOpenChange={setMoveDialogOpen}
        onConfirm={handleMoveConfirm}
      />
    </div>
  )
}

export default LocalResourcePanel
